"use client"

import React, { create<PERSON>ontext, useContext, useState, useEffect, useMemo } from "react"
import { ClientDataService, Client, ClientPreferences } from "@/lib/client-data-service"
import { useToast } from "@/components/ui/use-toast"
import { v4 as uuidv4 } from "uuid"
import { dataCache } from "@/lib/data-cache"

// Re-export types for convenience
export type { Client, ClientPreferences }

interface ClientContextType {
  clients: Client[]
  getClient: (id: string) => Client | undefined
  addClient: (client: Omit<Client, "id" | "avatar" | "segment" | "status">) => Client
  updateClient: (id: string, clientData: Partial<Client>) => Client | undefined
  deleteClient: (id: string) => boolean
  updateClientPreferences: (id: string, preferences: ClientPreferences) => Client | undefined
  updateClientSegment: (id: string, segment: Client["segment"]) => Client | undefined
  updateClientStatus: (id: string, status: Client["status"]) => Client | undefined
  // Auto-registration methods
  findClientByPhoneAndName: (phone: string, name: string) => Client | undefined
  autoRegisterClient: (clientData: {
    name: string
    email?: string
    phone: string
    source: "client_portal" | "appointment_booking" | "walk_in"
    preferredLocation?: string
  }) => Client | null
  normalizePhoneNumber: (phone: string) => string
  refreshClients: () => void
}

const ClientContext = createContext<ClientContextType>({
  clients: [],
  getClient: () => undefined,
  addClient: () => ({} as Client),
  updateClient: () => undefined,
  deleteClient: () => false,
  updateClientPreferences: () => undefined,
  updateClientSegment: () => undefined,
  updateClientStatus: () => undefined,
  findClientByPhoneAndName: () => undefined,
  autoRegisterClient: () => null,
  normalizePhoneNumber: () => "",
  refreshClients: () => {},
})

export function ClientProvider({ children }: { children: React.ReactNode }) {
  const [clients, setClients] = useState<Client[]>([])
  const [isInitialized, setIsInitialized] = useState(false)
  const { toast } = useToast()

  // Initialize with mock data on first load - only once, using data cache
  useEffect(() => {
    if (isInitialized) return;

    try {
      // Use the data cache to get clients efficiently
      const cachedClients = dataCache.getFromLocalStorage<Client[]>(
        "vanity_clients",
        ClientDataService.getClients(),
        { expiryTimeMs: 60 * 60 * 1000 } // 1 hour cache
      )

      // Ensure we have a valid array
      setClients(Array.isArray(cachedClients) ? cachedClients : ClientDataService.getClients())
    } catch (error) {
      console.error("Error loading clients:", error)
      // Fallback to data service if there's an error
      setClients(ClientDataService.getClients())
    }

    setIsInitialized(true)
  }, [isInitialized])

  // DEPRECATED: No localStorage saving - database is single source of truth
  useEffect(() => {
    if (!isInitialized || clients.length === 0) return;

    // Only dispatch events for real-time updates
    window.dispatchEvent(new CustomEvent('clients-updated', {
      detail: { clients, timestamp: Date.now() }
    }))
  }, [clients, isInitialized])

  // Listen for external client updates (e.g., from appointment booking)
  useEffect(() => {
    const handleExternalUpdate = () => {
      refreshClients()
    }

    window.addEventListener('refresh-clients', handleExternalUpdate)
    return () => window.removeEventListener('refresh-clients', handleExternalUpdate)
  }, [])

  // Get a client by ID
  const getClient = (id: string) => {
    return clients.find(client => client.id === id)
  }

  // Add a new client
  const addClient = (clientData: Omit<Client, "id" | "avatar" | "segment" | "status">) => {
    // Generate initials for avatar
    const nameParts = clientData.name.split(" ")
    const initials = nameParts.length > 1
      ? `${nameParts[0][0]}${nameParts[1][0]}`
      : nameParts[0].substring(0, 2)

    // Create new client with defaults
    const newClient: Client = {
      id: uuidv4(),
      avatar: initials.toUpperCase(),
      segment: "New",
      status: "Active",
      ...clientData,
    }

    setClients(prevClients => [...prevClients, newClient])

    toast({
      title: "Client created",
      description: `${newClient.name} has been added to your client database.`,
    })

    return newClient
  }

  // Update an existing client
  const updateClient = (id: string, clientData: Partial<Client>) => {
    let updatedClient: Client | undefined

    setClients(prevClients => {
      const updatedClients = prevClients.map(client => {
        if (client.id === id) {
          updatedClient = { ...client, ...clientData }
          return updatedClient
        }
        return client
      })

      return updatedClients
    })

    if (updatedClient) {
      toast({
        title: "Client updated",
        description: `${updatedClient.name}'s information has been updated.`,
      })
    }

    return updatedClient
  }

  // Delete a client
  const deleteClient = (id: string) => {
    const clientToDelete = getClient(id)

    if (!clientToDelete) return false

    setClients(prevClients => prevClients.filter(client => client.id !== id))

    toast({
      title: "Client deleted",
      description: `${clientToDelete.name} has been removed from your client database.`,
      variant: "destructive",
    })

    return true
  }

  // Update client preferences
  const updateClientPreferences = (id: string, preferences: ClientPreferences) => {
    return updateClient(id, { preferences })
  }

  // Update client segment
  const updateClientSegment = (id: string, segment: Client["segment"]) => {
    return updateClient(id, { segment })
  }

  // Update client status
  const updateClientStatus = (id: string, status: Client["status"]) => {
    return updateClient(id, { status })
  }

  // Normalize phone number for consistent comparison
  const normalizePhoneNumber = (phone: string): string => {
    // Remove all non-digit characters
    const digitsOnly = phone.replace(/\D/g, '')

    // Handle Qatar phone numbers
    if (digitsOnly.startsWith('974')) {
      return digitsOnly // Already has country code
    } else if (digitsOnly.startsWith('00974')) {
      return digitsOnly.substring(2) // Remove 00 prefix
    } else if (digitsOnly.length === 8) {
      return `974${digitsOnly}` // Add Qatar country code
    }

    return digitsOnly
  }

  // Find client by phone and name (duplicate detection)
  const findClientByPhoneAndName = (phone: string, name: string): Client | undefined => {
    const normalizedPhone = normalizePhoneNumber(phone)
    const normalizedName = name.toLowerCase().trim()

    return clients.find(client => {
      const clientNormalizedPhone = normalizePhoneNumber(client.phone)
      const clientNormalizedName = client.name.toLowerCase().trim()

      // Match if both phone AND name match
      return clientNormalizedPhone === normalizedPhone && clientNormalizedName === normalizedName
    })
  }

  // Auto-register client with duplicate detection
  const autoRegisterClient = (clientData: {
    name: string
    email?: string
    phone: string
    source: "client_portal" | "appointment_booking" | "walk_in"
    preferredLocation?: string
  }): Client | null => {
    // Check for existing client
    const existingClient = findClientByPhoneAndName(clientData.phone, clientData.name)

    if (existingClient) {
      console.log(`Client already exists: ${existingClient.name} (${existingClient.phone})`)
      return null // Client already exists, don't create duplicate
    }

    // Create new client with auto-registration metadata
    const newClientData = {
      name: clientData.name,
      email: clientData.email || "",
      phone: clientData.phone,
      preferredLocation: clientData.preferredLocation || "loc1",
      locations: [clientData.preferredLocation || "loc1"],
      registrationSource: clientData.source,
      isAutoRegistered: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      notes: `Auto-registered via ${clientData.source.replace('_', ' ')}`
    }

    const newClient = addClient(newClientData)

    console.log(`Auto-registered new client: ${newClient.name} via ${clientData.source}`)

    return newClient
  }

  // Refresh clients from storage
  const refreshClients = () => {
    try {
      const refreshedClients = ClientDataService.getClients()
      setClients(refreshedClients)
      console.log(`Refreshed ${refreshedClients.length} clients from storage`)
    } catch (error) {
      console.error("Error refreshing clients:", error)
    }
  }

  return (
    <ClientContext.Provider
      value={{
        clients,
        getClient,
        addClient,
        updateClient,
        deleteClient,
        updateClientPreferences,
        updateClientSegment,
        updateClientStatus,
        findClientByPhoneAndName,
        autoRegisterClient,
        normalizePhoneNumber,
        refreshClients,
      }}
    >
      {children}
    </ClientContext.Provider>
  )
}

export const useClients = () => useContext(ClientContext)
