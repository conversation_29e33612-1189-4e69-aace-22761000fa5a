const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkData() {
  try {
    console.log('=== SERVICES ===');
    const services = await prisma.service.findMany({
      take: 10,
      orderBy: { name: 'asc' }
    });
    console.log('Total services:', await prisma.service.count());
    console.log('Sample services:');
    services.forEach(s => console.log(`- ${s.name} (${s.category}) - $${s.price}`));
    
    console.log('\n=== CATEGORIES ===');
    const categories = await prisma.service.groupBy({
      by: ['category'],
      _count: { category: true }
    });
    console.log('Categories found:');
    categories.forEach(c => console.log(`- "${c.category}": ${c._count.category} services`));

    console.log('\n=== TESTING CATEGORY CONVERSION ===');
    categories.forEach(c => {
      const categoryId = c.category.toLowerCase().replace(/\s+/g, '-');
      const convertedBack = categoryId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      console.log(`Original: "${c.category}" -> ID: "${categoryId}" -> Converted back: "${convertedBack}" -> Match: ${c.category === convertedBack}`);
    });
    
    console.log('\n=== PRODUCTS ===');
    const products = await prisma.product.findMany({
      take: 5,
      orderBy: { name: 'asc' }
    });
    console.log('Total products:', await prisma.product.count());
    console.log('Sample products:');
    products.forEach(p => console.log(`- ${p.name} (${p.category || 'No category'}) - $${p.price || 'No price'}`));
    
    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
  }
}

checkData();
