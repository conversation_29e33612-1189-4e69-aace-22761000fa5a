// Using built-in fetch (Node 18+)

async function testAPI() {
  try {
    console.log('Testing /api/services...');
    const servicesResponse = await fetch('http://localhost:3000/api/services');
    const servicesData = await servicesResponse.json();
    console.log('Services API response:', {
      status: servicesResponse.status,
      servicesCount: servicesData.services?.length || 0,
      firstService: servicesData.services?.[0] || null
    });

    console.log('\nTesting /api/service-categories...');
    const categoriesResponse = await fetch('http://localhost:3000/api/service-categories');
    const categoriesData = await categoriesResponse.json();
    console.log('Categories API response:', {
      status: categoriesResponse.status,
      categoriesCount: categoriesData.categories?.length || 0,
      categories: categoriesData.categories?.map(c => ({ id: c.id, name: c.name, serviceCount: c.serviceCount })) || []
    });

    // Test a specific category
    if (categoriesData.categories?.length > 0) {
      const firstCategory = categoriesData.categories[0];
      console.log(`\nTesting /api/service-categories/${firstCategory.id}...`);
      const categoryResponse = await fetch(`http://localhost:3000/api/service-categories/${firstCategory.id}`);
      const categoryData = await categoryResponse.json();
      console.log('Category services response:', {
        status: categoryResponse.status,
        categoryName: firstCategory.name,
        servicesCount: categoryData.services?.length || 0,
        firstService: categoryData.services?.[0]?.name || 'None'
      });
    }

  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAPI();
