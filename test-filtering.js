// Test the filtering logic
const services = [
  { id: '1', name: 'Box Braid Small', category: 'Braiding', price: 520 },
  { id: '2', name: 'Hair Color', category: 'Color', price: 200 },
  { id: '3', name: 'Nail Art', category: 'Nail', price: 150 },
  { id: '4', name: 'Massage', category: 'Massage And Spa', price: 300 }
];

const categories = [
  { id: 'braiding', name: 'Braiding' },
  { id: 'color', name: 'Color' },
  { id: 'nail', name: 'Nail' },
  { id: 'massage-and-spa', name: 'Massage And Spa' }
];

function testFiltering(selectedCategory) {
  console.log(`\nTesting category: ${selectedCategory}`);
  
  const filtered = services.filter(service => {
    const matchesCategory = selectedCategory === "all" ||
                        service.category === selectedCategory ||
                        service.category.toLowerCase().replace(/\s+/g, '-') === selectedCategory;
    return matchesCategory;
  });
  
  console.log(`Found ${filtered.length} services:`);
  filtered.forEach(s => console.log(`- ${s.name} (${s.category})`));
}

// Test all categories
testFiltering('all');
testFiltering('braiding');
testFiltering('color');
testFiltering('nail');
testFiltering('massage-and-spa');
