// Vanity Hub Database Schema
// Comprehensive salon management system

generator client {
  provider = "prisma-client-js"
}

// ERD generator disabled for now
// generator erd {
//   provider = "prisma-erd-generator"
//   output   = "../docs/ERD.svg"
// }

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  role      UserRole @default(CLIENT)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  staffProfile   StaffMember?
  clientProfile  Client?
  appointments   Appointment[]
  transactions   Transaction[]
  auditLogs      AuditLog[]

  @@map("users")
}

enum UserRole {
  ADMIN
  MANAGER
  STAFF
  CLIENT
}

// ============================================================================
// STAFF MANAGEMENT
// ============================================================================

model StaffMember {
  id               String      @id @default(cuid())
  userId           String      @unique
  name             String
  phone            String?
  avatar           String?
  color            String?
  jobRole          String?     // Specific job role (stylist, colorist, etc.)
  dateOfBirth      DateTime?   // Date of birth for HR management
  homeService      Boolean     @default(false)
  status           StaffStatus @default(ACTIVE)
  // HR Document Management Fields
  employeeNumber   String?     // Employee number for HR tracking
  qidNumber        String?     // Qatar ID number
  passportNumber   String?     // Passport number
  qidValidity      String?     // Qatar ID expiration date in MM-DD-YY format
  passportValidity String?     // Passport expiration date in MM-DD-YY format
  medicalValidity  String?     // Medical certificate expiration date in MM-DD-YY format
  profileImage     String?     // Base64 encoded image or URL
  profileImageType String?     // MIME type of the image
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt

  // Relationships
  user         User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  locations    StaffLocation[]
  appointments Appointment[]
  services     StaffService[]
  schedule     StaffSchedule[]

  @@map("staff_members")
}

enum StaffStatus {
  ACTIVE
  INACTIVE
  ON_LEAVE
}

model StaffSchedule {
  id        String   @id @default(cuid())
  staffId   String
  dayOfWeek Int      // 0 = Sunday, 1 = Monday, etc.
  startTime String   // HH:MM format
  endTime   String   // HH:MM format
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  staff StaffMember @relation(fields: [staffId], references: [id], onDelete: Cascade)

  @@unique([staffId, dayOfWeek])
  @@map("staff_schedules")
}

// ============================================================================
// CLIENT MANAGEMENT
// ============================================================================

model Client {
  id          String   @id @default(cuid())
  userId      String   @unique
  name        String
  phone       String?
  dateOfBirth DateTime?
  preferences String?
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  user           User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  loyaltyProgram LoyaltyProgram?

  @@map("clients")
}

// ============================================================================
// LOCATION MANAGEMENT
// ============================================================================

model Location {
  id        String   @id @default(cuid())
  name      String
  address   String
  city      String
  state     String?
  zipCode   String?
  country   String
  phone     String?
  email     String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  staff        StaffLocation[]
  appointments Appointment[]
  services     LocationService[]
  products     ProductLocation[]
  transactions Transaction[]

  @@map("locations")
}

// ============================================================================
// SERVICES MANAGEMENT
// ============================================================================

model Service {
  id          String   @id @default(cuid())
  name        String
  description String?
  duration    Int      // in minutes
  price       Decimal
  category    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  locations    LocationService[]
  staff        StaffService[]
  appointments AppointmentService[]

  @@map("services")
}

// ============================================================================
// APPOINTMENTS MANAGEMENT
// ============================================================================

model Appointment {
  id               String            @id @default(cuid())
  bookingReference String            @unique
  clientId         String
  staffId          String
  locationId       String
  date             DateTime
  duration         Int               // in minutes
  totalPrice       Decimal
  status           AppointmentStatus @default(PENDING)
  notes            String?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt

  // Relationships
  client   User        @relation(fields: [clientId], references: [id])
  staff    StaffMember @relation(fields: [staffId], references: [id])
  location Location    @relation(fields: [locationId], references: [id])
  services AppointmentService[]
  products AppointmentProduct[]

  @@map("appointments")
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  NO_SHOW
}

// ============================================================================
// JUNCTION TABLES
// ============================================================================

model StaffLocation {
  id         String   @id @default(cuid())
  staffId    String
  locationId String
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())

  // Relationships
  staff    StaffMember @relation(fields: [staffId], references: [id], onDelete: Cascade)
  location Location    @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@unique([staffId, locationId])
  @@map("staff_locations")
}

model StaffService {
  id        String   @id @default(cuid())
  staffId   String
  serviceId String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())

  // Relationships
  staff   StaffMember @relation(fields: [staffId], references: [id], onDelete: Cascade)
  service Service     @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@unique([staffId, serviceId])
  @@map("staff_services")
}

model LocationService {
  id         String   @id @default(cuid())
  locationId String
  serviceId  String
  price      Decimal? // Location-specific pricing
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())

  // Relationships
  location Location @relation(fields: [locationId], references: [id], onDelete: Cascade)
  service  Service  @relation(fields: [serviceId], references: [id], onDelete: Cascade)

  @@unique([locationId, serviceId])
  @@map("location_services")
}

model AppointmentService {
  id            String   @id @default(cuid())
  appointmentId String
  serviceId     String
  price         Decimal
  duration      Int      // in minutes
  createdAt     DateTime @default(now())

  // Relationships
  appointment Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  service     Service     @relation(fields: [serviceId], references: [id])

  @@unique([appointmentId, serviceId])
  @@map("appointment_services")
}

// ============================================================================
// PRODUCTS MANAGEMENT
// ============================================================================

model Product {
  id          String      @id @default(cuid())
  name        String
  description String?
  price       Float
  cost        Float?
  category    String
  type        ProductType @default(OTHER)
  brand       String?
  sku         String?     @unique
  barcode     String?
  image       String?
  isRetail    Boolean     @default(false)
  isActive    Boolean     @default(true)
  isFeatured  Boolean     @default(false)
  isNew       Boolean     @default(false)
  isBestSeller Boolean    @default(false)
  isSale      Boolean     @default(false)
  salePrice   Float?
  rating      Float?      @default(0)
  reviewCount Int         @default(0)
  features    String?     // JSON string for array data
  ingredients String?     // JSON string for array data
  howToUse    String?     // JSON string for array data
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relationships
  locations    ProductLocation[]
  appointments AppointmentProduct[]

  @@map("products")
}

enum ProductType {
  // Skincare specific types
  DAILY_CLEANSER
  TREATMENT_SERUM
  HYDRATING_CREAM
  ANTI_AGING_TREATMENT
  WEEKLY_MASK

  // Makeup specific types
  LIQUID_FOUNDATION
  EYE_ENHANCER
  LIP_COLOR

  // Hair Care specific types
  DAILY_SHAMPOO
  INTENSIVE_TREATMENT

  // Hair Extensions specific types
  TEMPORARY_EXTENSIONS
  SEMI_PERMANENT_EXTENSIONS

  // Nail Care specific types
  COLOR_POLISH
  NAIL_TREATMENT

  // Fragrance specific types
  LUXURY_FRAGRANCE

  // Personal Care specific types
  BODY_MOISTURIZER

  // Specialty specific types
  TARGETED_TREATMENT

  // Tools specific types
  APPLICATION_TOOLS
  STYLING_EQUIPMENT

  // Legacy types (keep for compatibility)
  SKINCARE
  MAKEUP
  HAIR_CARE
  HAIR_EXTENSIONS
  NAIL_CARE
  FRAGRANCE
  PERSONAL_CARE
  SPECIALTY
  TOOLS
  ACCESSORIES
  OTHER
}

model ProductLocation {
  id         String   @id @default(cuid())
  productId  String
  locationId String
  stock      Int      @default(0)
  price      Float?   // Location-specific pricing
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relationships
  product  Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  location Location @relation(fields: [locationId], references: [id], onDelete: Cascade)

  @@unique([productId, locationId])
  @@map("product_locations")
}

model AppointmentProduct {
  id            String   @id @default(cuid())
  appointmentId String
  productId     String
  quantity      Int      @default(1)
  price         Float
  createdAt     DateTime @default(now())

  // Relationships
  appointment Appointment @relation(fields: [appointmentId], references: [id], onDelete: Cascade)
  product     Product     @relation(fields: [productId], references: [id])

  @@map("appointment_products")
}

// ============================================================================
// FINANCIAL MANAGEMENT
// ============================================================================

model Transaction {
  id            String            @id @default(cuid())
  userId        String
  amount        Decimal
  type          TransactionType
  status        TransactionStatus @default(PENDING)
  method        PaymentMethod
  reference     String?
  description   String?
  locationId    String?
  appointmentId String?
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relationships
  user     User      @relation(fields: [userId], references: [id])
  location Location? @relation(fields: [locationId], references: [id])

  @@map("transactions")
}

enum TransactionType {
  PAYMENT
  REFUND
  CREDIT
  DEBIT
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum PaymentMethod {
  CASH
  CARD
  BANK_TRANSFER
  DIGITAL_WALLET
  LOYALTY_POINTS
}

// ============================================================================
// LOYALTY PROGRAM
// ============================================================================

model LoyaltyProgram {
  id           String   @id @default(cuid())
  clientId     String   @unique
  points       Int      @default(0)
  tier         String   @default("Bronze")
  totalSpent   Decimal  @default(0)
  joinDate     DateTime @default(now())
  lastActivity DateTime @default(now())
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relationships
  client Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@map("loyalty_programs")
}

// ============================================================================
// AUDIT & LOGGING
// ============================================================================

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  entity    String
  entityId  String?
  oldValues Json?
  newValues Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relationships
  user User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

// ============================================================================
// INDEXES FOR PERFORMANCE
// ============================================================================

// Add indexes for frequently queried fields
// These will be created during migration
