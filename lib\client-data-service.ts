// DEPRECATED: This service is no longer used
// All client data is now persisted in the database through Prisma
// Use API endpoints to fetch real client data

"use client"

// Client preferences interface
export interface ClientPreferences {
  preferredStylists: string[]
  preferredServices: string[]
  preferredProducts: string[]
  allergies: string[]
  notes: string
}

// Client interface
export interface Client {
  id: string
  name: string
  email: string
  phone: string
  address?: string
  city?: string
  state?: string
  zip?: string
  birthday?: string
  lastVisit?: string
  preferredLocation?: string
  locations?: string[]
  status: "Active" | "Inactive" | "Pending"
  avatar?: string
  segment?: "VIP" | "Regular" | "New" | "At Risk"
  totalSpent?: number
  referredBy?: string
  preferences?: ClientPreferences
  notes?: string
  // Auto-registration tracking
  registrationSource?: "manual" | "client_portal" | "appointment_booking" | "walk_in"
  isAutoRegistered?: boolean
  createdAt?: string
  updatedAt?: string
}

// Default client data for initial setup
const defaultClients: Client[] = [
  {
    id: "ed1",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Apr 1, 2025",
    preferredLocation: "loc1",
    locations: ["loc1", "loc2"],
    status: "Active",
    avatar: "ED",
    segment: "VIP",
    totalSpent: 1250.75,
    preferences: {
      preferredStylists: ["Emma Johnson", "Sarah Chen"],
      preferredServices: ["Haircut & Style", "Color & Highlights"],
      preferredProducts: ["Premium Shampoo", "Color Protection Serum"],
      allergies: [],
      notes: "Prefers morning appointments"
    },
    notes: "Regular client since 2023"
  },
  {
    id: "jw2",
    name: "James Wilson",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 25, 2025",
    preferredLocation: "loc1",
    locations: ["loc1"],
    status: "Active",
    avatar: "JW",
    segment: "Regular",
    totalSpent: 875.5,
    preferences: {
      preferredStylists: ["Michael Chen"],
      preferredServices: ["Men's Haircut", "Beard Trim"],
      preferredProducts: ["Styling Gel", "Beard Oil"],
      allergies: [],
      notes: "Afternoon appointments preferred"
    },
    notes: "Loyal customer"
  },
  {
    id: "sw3",
    name: "Sarah Williams",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 20, 2025",
    preferredLocation: "loc2",
    locations: ["loc2", "loc3"],
    status: "Active",
    avatar: "SW",
    segment: "Regular",
    totalSpent: 650.25,
    preferences: {
      preferredStylists: ["Lisa Rodriguez"],
      preferredServices: ["Haircut & Style", "Deep Conditioning"],
      preferredProducts: ["Sulfate-Free Shampoo", "Organic Conditioner"],
      allergies: ["Sulfates", "Parabens"],
      notes: "Allergic to certain hair products"
    },
    notes: "Detail-oriented client"
  },
  {
    id: "ma4",
    name: "Michael Anderson",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 18, 2025",
    preferredLocation: "loc1",
    locations: ["loc1"],
    status: "Active",
    avatar: "MA",
    segment: "Regular",
    totalSpent: 420.0,
    preferences: {
      preferredStylists: ["David Kim"],
      preferredServices: ["Quick Cut", "Wash & Style"],
      preferredProducts: [],
      allergies: [],
      notes: "Quick services preferred"
    },
    notes: "Busy professional"
  },
  {
    id: "lb5",
    name: "Lisa Brown",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 15, 2025",
    preferredLocation: "loc3",
    locations: ["loc3"],
    status: "Active",
    avatar: "LB",
    segment: "VIP",
    totalSpent: 1850.0,
    preferences: {
      preferredStylists: ["Emma Johnson", "Sarah Chen"],
      preferredServices: ["Premium Color", "Keratin Treatment", "Luxury Facial"],
      preferredProducts: ["Premium Hair Mask", "Anti-Aging Serum", "Luxury Moisturizer"],
      allergies: [],
      notes: "Premium services only"
    },
    notes: "High-value client"
  },
  {
    id: "dj6",
    name: "David Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 12, 2025",
    preferredLocation: "loc2",
    locations: ["loc2"],
    status: "Active",
    avatar: "DJ",
    segment: "Regular",
    totalSpent: 320.5,
    preferences: {
      preferredStylists: [],
      preferredServices: ["Haircut & Style"],
      preferredProducts: [],
      allergies: [],
      notes: "Weekend appointments"
    },
    notes: "Flexible with services"
  },
  {
    id: "jd7",
    name: "Jennifer Davis",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 10, 2025",
    preferredLocation: "loc1",
    locations: ["loc1", "loc2"],
    status: "Active",
    avatar: "JD",
    segment: "Regular",
    totalSpent: 780.25,
    preferences: {
      preferredStylists: ["Sarah Chen", "Lisa Rodriguez"],
      preferredServices: ["Color & Highlights", "Balayage", "Hair Treatment"],
      preferredProducts: ["Color Protection Shampoo", "Hair Mask"],
      allergies: [],
      notes: "Color services specialist"
    },
    notes: "Loves trying new styles"
  },
  {
    id: "rm8",
    name: "Robert Miller",
    email: "<EMAIL>",
    phone: "(*************",
    lastVisit: "Mar 8, 2025",
    preferredLocation: "loc3",
    locations: ["loc3"],
    status: "Active",
    avatar: "RM",
    segment: "New",
    totalSpent: 125.0,
    preferences: {
      preferredStylists: [],
      preferredServices: ["Basic Haircut"],
      preferredProducts: [],
      allergies: [],
      notes: "Simple cuts preferred"
    },
    notes: "New to the area"
  }
]

// DEPRECATED: All localStorage functions removed
// Use API endpoints instead

// Migration function to convert old string preferences to new object structure
function migrateClientPreferences(clients: any[]): Client[] {
  return clients.map(client => {
    // If preferences is a string, convert it to the new object structure
    if (typeof client.preferences === 'string') {
      const oldPreferencesNote = client.preferences
      client.preferences = {
        preferredStylists: [],
        preferredServices: [],
        preferredProducts: [],
        allergies: [],
        notes: oldPreferencesNote
      }
    }
    // If preferences is undefined, initialize with empty structure
    else if (!client.preferences) {
      client.preferences = {
        preferredStylists: [],
        preferredServices: [],
        preferredProducts: [],
        allergies: [],
        notes: ""
      }
    }
    return client as Client
  })
}

// Client Data Service
export const ClientDataService = {
  // Initialize clients with default data if none exists
  initializeClients: (): Client[] => {
    console.log("ClientDataService: Initializing clients...")

    const existingClients = getFromStorage<Client[]>(STORAGE_KEY, [])
    if (existingClients.length > 0) {
      console.log("ClientDataService: Found existing clients:", existingClients.length)
      // Migrate existing clients to new preferences structure
      const migratedClients = migrateClientPreferences(existingClients)
      // Save migrated data back to storage
      saveToStorage(STORAGE_KEY, migratedClients)
      console.log("ClientDataService: Migrated client preferences to new structure")
      return migratedClients
    }

    console.log("ClientDataService: No existing clients found, initializing with defaults")
    const clientsWithTimestamps = defaultClients.map(client => ({
      ...client,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }))

    saveToStorage(STORAGE_KEY, clientsWithTimestamps)
    console.log("ClientDataService: Initialized", clientsWithTimestamps.length, "default clients")
    return clientsWithTimestamps
  },

  // DEPRECATED: Use API endpoints instead
  getClients: (): Client[] => {
    console.warn("ClientDataService.getClients is deprecated. Use GET /api/clients instead.")
    return []
  },

  // Get client by ID
  getClientById: (id: string): Client | undefined => {
    const clients = ClientDataService.getClients()
    return clients.find(client => client.id === id)
  },

  // Get clients by location
  getClientsByLocation: (locationId: string): Client[] => {
    const clients = ClientDataService.getClients()
    return clients.filter(client => 
      client.locations?.includes(locationId) || client.preferredLocation === locationId
    )
  },

  // Get clients by segment
  getClientsBySegment: (segment: string): Client[] => {
    const clients = ClientDataService.getClients()
    return clients.filter(client => client.segment === segment)
  },

  // Search clients
  searchClients: (query: string): Client[] => {
    const clients = ClientDataService.getClients()
    const lowercaseQuery = query.toLowerCase()
    
    return clients.filter(client =>
      client.name.toLowerCase().includes(lowercaseQuery) ||
      client.email.toLowerCase().includes(lowercaseQuery) ||
      client.phone.includes(query)
    )
  },

  // DEPRECATED: Use API endpoints instead
  saveClients: (clients: Client[]) => {
    console.warn("ClientDataService.saveClients is deprecated. Use POST/PUT /api/clients instead.")
  },

  // DEPRECATED: Use API endpoints instead
  addClient: (clientData: Omit<Client, "id" | "createdAt" | "updatedAt">): Client => {
    console.warn("ClientDataService.addClient is deprecated. Use POST /api/clients instead.")
    return {
      ...clientData,
      id: 'deprecated',
      status: "Active",
      segment: "New",
      locations: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },

  // DEPRECATED: Use API endpoints instead
  updateClient: (id: string, updates: Partial<Client>): Client | null => {
    console.warn("ClientDataService.updateClient is deprecated. Use PUT /api/clients/[id] instead.")
    return null
  },

  // DEPRECATED: Use API endpoints instead
  deleteClient: (id: string): boolean => {
    console.warn("ClientDataService.deleteClient is deprecated. Use DELETE /api/clients/[id] instead.")
    return false
  },

  // Get client statistics
  getClientStats: () => {
    const clients = ClientDataService.getClients()
    const activeClients = clients.filter(client => client.status === "Active")
    
    return {
      total: clients.length,
      active: activeClients.length,
      vip: clients.filter(client => client.segment === "VIP").length,
      regular: clients.filter(client => client.segment === "Regular").length,
      new: clients.filter(client => client.segment === "New").length,
      totalSpent: clients.reduce((sum, client) => sum + (client.totalSpent || 0), 0)
    }
  }
}
